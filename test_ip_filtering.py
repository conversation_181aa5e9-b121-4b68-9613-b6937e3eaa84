#!/usr/bin/env python3
"""
测试本机IP过滤逻辑
"""

import socket

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        print(f"获取本机IP失败: {str(e)}")
        return None

def test_ip_filtering():
    """测试IP过滤逻辑"""
    
    # 获取本机IP
    local_ip = get_local_ip()
    print(f"本机IP: {local_ip}")
    
    # 模拟扫描到的活跃IP列表
    active_ips = [
        "*************",
        "*************", 
        "*************",
        local_ip,  # 包含本机IP
        "*************"
    ]
    
    print(f"扫描到的活跃IP: {active_ips}")
    
    # 过滤掉本机IP
    remote_ips = [ip for ip in active_ips if ip != local_ip]
    
    print(f"过滤后的远程IP: {remote_ips}")
    print(f"本机IP是否被过滤: {'是' if local_ip not in remote_ips else '否'}")
    print(f"远程设备数量: {len(remote_ips)}")
    
    # 验证过滤结果
    assert local_ip not in remote_ips, "本机IP应该被过滤掉"
    assert len(remote_ips) == len(active_ips) - 1, "远程IP数量应该比原始数量少1"
    
    print("✅ IP过滤测试通过！")

def test_device_list_logic():
    """测试设备列表显示逻辑"""
    
    local_ip = get_local_ip()
    
    test_cases = [
        {
            'name': '有远程设备',
            'active_ips': [local_ip, '*************', '*************'],
            'expected_remote_count': 2
        },
        {
            'name': '只有本机',
            'active_ips': [local_ip],
            'expected_remote_count': 0
        },
        {
            'name': '没有本机IP',
            'active_ips': ['*************', '*************'],
            'expected_remote_count': 2
        }
    ]
    
    for case in test_cases:
        active_ips = case['active_ips']
        remote_ips = [ip for ip in active_ips if ip != local_ip]
        
        print(f"\n测试案例: {case['name']}")
        print(f"  活跃IP: {active_ips}")
        print(f"  远程IP: {remote_ips}")
        print(f"  期望远程设备数: {case['expected_remote_count']}")
        print(f"  实际远程设备数: {len(remote_ips)}")
        
        assert len(remote_ips) == case['expected_remote_count'], f"{case['name']}: 远程设备数量不匹配"
        
        # 模拟设备列表显示逻辑
        if remote_ips:
            device_display = remote_ips
        else:
            device_display = ["无远程设备"]
            
        print(f"  设备列表显示: {device_display}")
        print(f"  ✅ {case['name']} 测试通过")

if __name__ == "__main__":
    print("开始测试IP过滤逻辑...")
    test_ip_filtering()
    print("\n" + "="*50)
    print("开始测试设备列表逻辑...")
    test_device_list_logic()
    print("\n🎉 所有测试完成！")
