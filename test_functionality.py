#!/usr/bin/env python3
"""
测试本地文件播放和定位功能
"""

import os
import tempfile
from pathlib import Path

def test_file_operations():
    """测试文件操作功能"""
    
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
        temp_file.write(b'test video content')
        test_file_path = temp_file.name
    
    try:
        # 模拟本地文件信息
        local_file_info = {
            'filename': os.path.basename(test_file_path),
            'path': test_file_path,
            'local': True,
            'ip': '本机',
            'size': os.path.getsize(test_file_path),
            'mtime': os.path.getmtime(test_file_path)
        }
        
        # 模拟远程文件信息
        remote_file_info = {
            'filename': 'remote_video.mp4',
            'path': 'D:/videos/20250903/remote_video.mp4',
            'local': False,
            'ip': '*************',
            'size': 1024000,
            'mtime': 1234567890
        }
        
        print("测试文件信息:")
        print(f"本地文件: {local_file_info}")
        print(f"远程文件: {remote_file_info}")
        
        # 测试文件存在性检查
        local_path = Path(local_file_info['path'])
        print(f"\n本地文件存在性检查: {local_path.exists()}")
        
        # 测试文件夹路径
        print(f"本地文件父目录: {local_path.parent}")
        print(f"父目录存在性: {local_path.parent.exists()}")
        
        print("\n✅ 文件操作测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

def test_menu_logic():
    """测试菜单逻辑"""
    
    # 模拟不同的选择情况
    test_cases = [
        {
            'name': '只选择本地文件',
            'files': [{'local': True}, {'local': True}],
            'expected': {'has_local': True, 'has_remote': False}
        },
        {
            'name': '只选择远程文件',
            'files': [{'local': False}, {'local': False}],
            'expected': {'has_local': False, 'has_remote': True}
        },
        {
            'name': '混合选择',
            'files': [{'local': True}, {'local': False}],
            'expected': {'has_local': True, 'has_remote': True}
        }
    ]
    
    for case in test_cases:
        files = case['files']
        has_local = any(f['local'] for f in files)
        has_remote = any(not f['local'] for f in files)
        
        expected = case['expected']
        assert has_local == expected['has_local'], f"{case['name']}: has_local 不匹配"
        assert has_remote == expected['has_remote'], f"{case['name']}: has_remote 不匹配"
        
        print(f"✅ {case['name']}: has_local={has_local}, has_remote={has_remote}")
    
    print("\n✅ 菜单逻辑测试完成")

if __name__ == "__main__":
    print("开始测试文件操作功能...")
    test_file_operations()
    print("\n" + "="*50)
    print("开始测试菜单逻辑...")
    test_menu_logic()
    print("\n🎉 所有测试完成！")
