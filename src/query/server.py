import sys
import socket
import json
import os
import logging
import tempfile
import threading
import subprocess
from datetime import datetime
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLineEdit, QPushButton, QListWidget, QMessageBox, QProgressDialog,
    QLabel, QDialog, QProgressBar, QMenu, QFileDialog, QDialogButtonBox, QListWidgetItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize, QUrl, QTimer, QObject
from PyQt6.QtGui import QColor, QDesktopServices, QIcon, QAction, QShortcut, QKeySequence
import excel_importer
import video_processor

# 日志配置
logging.basicConfig(
    filename='app.log',
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(message)s'
)

# 配置信息
CONFIG = {
    "PORT": 65432,
    "TIMEOUT": 2,
    "SCAN_WORKERS": 100,
    "SEARCH_DIRS": ['D:/videos', 'E:/videos', 'F:/videos', 'G:/videos'],
    "VIDEO_EXTS": ['.mp4', '.avi', '.mov', '.mkv', '.flv'],
    "DB": {
        "host": "************",
        "user": "root",
        "password": "ZhangLong1215",
        "database": "logistics",
        "charset": "utf8mb4"
    }
}

class FileServer(QThread):
    status_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.server_socket = None

    def run(self):
        self.running = True
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', CONFIG["PORT"]))
            self.server_socket.listen(5)
            self.status_changed.emit("服务已启动")
            logging.info("Server started on port %d", CONFIG["PORT"])

            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, addr),
                        daemon=True
                    ).start()
                except OSError:
                    break
        except Exception as e:
            self.status_changed.emit(f"服务启动失败: {str(e)}")
            logging.error("Server start failed: %s", str(e))
        finally:
            if self.server_socket:
                self.server_socket.close()

    def stop(self):
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except Exception as e:
                logging.error("Error closing socket: %s", str(e))
        self.quit()
        self.wait()

    def handle_client(self, client_socket, addr):
        try:
            with client_socket:
                data = client_socket.recv(1024).decode()
                logging.info(f"来自 {addr[0]} 的请求: {data[:100]}")
                if data.startswith("SEARCH:"):
                    self.handle_search(client_socket, data[7:])
                elif data.startswith("DOWNLOAD:"):
                    self.handle_download(client_socket, data[9:])
        except Exception as e:
            logging.error("Client handling error: %s", str(e))

    def handle_search(self, sock, sn):
        try:
            results = []
            logging.info(f"开始搜索: {sn}")
            for base_dir in CONFIG["SEARCH_DIRS"]:
                results.extend(self.search_in_date_folders(base_dir, sn))
            logging.info(f"找到 {len(results)} 个结果")
            sock.sendall(json.dumps(results).encode())
        except Exception as e:
            logging.error("Search error: %s", str(e))

    def search_in_date_folders(self, base_dir, sn):
        """在日期文件夹中搜索指定单号的视频文件"""
        results = []
        base_path = Path(base_dir)

        if not base_path.exists():
            return results

        try:
            # 遍历所有日期文件夹（格式：YYYYMMDD）
            for date_folder in base_path.iterdir():
                if date_folder.is_dir() and self.is_date_folder(date_folder.name):
                    # 在日期文件夹中查找匹配单号的视频文件
                    for video_file in date_folder.glob('*.*'):
                        if (video_file.suffix.lower() in CONFIG["VIDEO_EXTS"] and
                            self.matches_sn(video_file.stem, sn)):
                            results.append({
                                'filename': video_file.name,
                                'path': str(video_file.resolve()),
                                'drive': base_dir[0],
                                'size': video_file.stat().st_size,
                                'mtime': video_file.stat().st_mtime
                            })
        except Exception as e:
            logging.error(f"搜索日期文件夹失败 {base_dir}: {str(e)}")

        return results

    def is_date_folder(self, folder_name):
        """检查文件夹名是否为日期格式（YYYYMMDD）"""
        import re
        return bool(re.match(r'^\d{8}$', folder_name))

    def matches_sn(self, filename, sn):
        """检查文件名是否匹配单号"""
        # 精确匹配单号
        return filename.upper() == sn.upper()



    def handle_download(self, sock, filepath):
        try:
            if not Path(filepath).exists():
                sock.send(b'FILE_NOT_FOUND')
                return

            with open(filepath, 'rb') as f:
                file_size = os.path.getsize(filepath)
                sock.send(f"SIZE:{file_size}".encode())
                ack = sock.recv(1024)
                if ack != b'ACK':
                    return

                while chunk := f.read(4096):
                    sock.sendall(chunk)
            logging.info(f"文件 {filepath} 发送成功")
        except Exception as e:
            logging.error("File transfer error: %s", str(e))

class NetworkScanner(QThread):
    progress_updated = pyqtSignal(int)
    scan_finished = pyqtSignal(list)

    def run(self):
        base_ip = self.get_base_ip()
        active_ips = []
        logging.info(f"开始网络扫描，基准IP: {base_ip}")

        with ThreadPoolExecutor(max_workers=CONFIG["SCAN_WORKERS"]) as executor:
            futures = {
                executor.submit(self.check_ip, f"{base_ip}{i}"): i
                for i in range(1, 255)
            }

            for i, future in enumerate(as_completed(futures), 1):
                if future.result():
                    ip_num = futures[future]
                    active_ips.append(f"{base_ip}{ip_num}")
                self.progress_updated.emit(int(i * 100 / 254))

        self.scan_finished.emit(active_ips)

    def get_base_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return '.'.join(ip.split('.')[:3]) + '.'
        except:
            return '192.168.1.'

    def check_ip(self, ip):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                s.connect((ip, CONFIG["PORT"]))
                return True
        except:
            return False

class FileSearcher(QThread):
    search_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, sn, active_ips):
        super().__init__()
        self.sn = sn
        self.active_ips = active_ips
        self.local_ip = self.get_local_ip()

    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            logging.info(f"检测到本机IP: {ip}")
            return ip
        except Exception as e:
            logging.error(f"获取本机IP失败: {str(e)}")
            return None

    def run(self):
        try:
            results = []
            results.extend(self.search_local())

            # 过滤掉本机IP，避免重复查询
            remote_ips = [ip for ip in self.active_ips if ip != self.local_ip]
            logging.info(f"本机IP: {self.local_ip}, 远程IP数量: {len(remote_ips)}")

            if remote_ips:
                with ThreadPoolExecutor(max_workers=20) as executor:
                    futures = {
                        executor.submit(self.query_host, ip): ip
                        for ip in remote_ips
                    }

                    for future in as_completed(futures):
                        ip = futures[future]
                        try:
                            host_results = future.result()
                            if host_results:
                                results.extend(host_results)
                        except Exception as e:
                            logging.error(f"查询 {ip} 失败: {str(e)}")

            self.search_finished.emit(results)
        except Exception as e:
            self.error_occurred.emit(str(e))

    def search_local(self):
        local_results = []
        for base_dir in CONFIG["SEARCH_DIRS"]:
            results = self.search_in_local_date_folders(base_dir, self.sn)
            local_results.extend([
                {**item, 'ip': '本机', 'local': True}
                for item in results
            ])
        return local_results

    def search_in_local_date_folders(self, base_dir, sn):
        """在本地日期文件夹中搜索指定单号的视频文件"""
        results = []
        base_path = Path(base_dir)

        if not base_path.exists():
            return results

        try:
            # 遍历所有日期文件夹（格式：YYYYMMDD）
            for date_folder in base_path.iterdir():
                if date_folder.is_dir() and self.is_date_folder(date_folder.name):
                    # 在日期文件夹中查找匹配单号的视频文件
                    for video_file in date_folder.glob('*.*'):
                        if (video_file.suffix.lower() in CONFIG["VIDEO_EXTS"] and
                            self.matches_sn(video_file.stem, sn)):
                            results.append({
                                'filename': video_file.name,
                                'path': str(video_file.resolve()),
                                'drive': base_dir[0],
                                'size': video_file.stat().st_size,
                                'mtime': video_file.stat().st_mtime
                            })
        except Exception as e:
            logging.error(f"本地搜索日期文件夹失败 {base_dir}: {str(e)}")

        return results





    def query_host(self, ip):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(CONFIG["TIMEOUT"])
                s.connect((ip, CONFIG["PORT"]))
                s.send(f"SEARCH:{self.sn}".encode())

                buffer = []
                while True:
                    data = s.recv(4096)
                    if not data:
                        break
                    buffer.append(data.decode())

                response = ''.join(buffer)
                if not response:
                    return []

                items = json.loads(response)
                return [
                    {**item, 'ip': ip, 'local': False}
                    for item in items
                ]
        except Exception as e:
            logging.error(f"查询 {ip} 失败: {str(e)}")
            return []

class DownloadManager(QThread):
    progress_updated = pyqtSignal(int)
    download_finished = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, files, save_dir, parent=None):
        super().__init__(parent)
        self.files = files
        self.save_dir = save_dir
        self._cancel = False
        if parent:
            parent.download_tasks.append(self)

    def run(self):
        try:
            os.makedirs(self.save_dir, exist_ok=True)

            for i, file_info in enumerate(self.files, 1):
                if self._cancel:
                    break

                if file_info['local']:
                    self.handle_local_file(file_info)
                else:
                    self.download_remote_file(file_info)

                self.progress_updated.emit(int(i * 100 / len(self.files)))
        except Exception as e:
            self.error_occurred.emit(str(e))
        finally:
            if self.parent():
                self.parent().download_tasks.remove(self)

    def handle_local_file(self, file_info):
        link_path = Path(self.save_dir) / file_info['filename']
        if not link_path.exists():
            try:
                os.symlink(file_info['path'], str(link_path))
            except OSError:
                self.create_shortcut(file_info['path'], str(link_path))

    def create_shortcut(self, target, shortcut_path):
        try:
            from win32com.client import Dispatch
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = target
            shortcut.save()
        except ImportError:
            with open(shortcut_path + '.url', 'w') as f:
                f.write('[InternetShortcut]\nURL=file:///{}'.format(target))

    def download_remote_file(self, file_info):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(CONFIG["TIMEOUT"])
                s.connect((file_info['ip'], CONFIG["PORT"]))
                s.send(f"DOWNLOAD:{file_info['path']}".encode())

                response = s.recv(1024).decode()
                if response.startswith('SIZE:'):
                    file_size = int(response[5:])
                    s.send(b'ACK')
                else:
                    raise Exception("无效的服务器响应")

                save_path = Path(self.save_dir) / file_info['filename']
                received = 0

                with open(save_path, 'wb') as f:
                    while received < file_size:
                        if self._cancel:
                            break
                        data = s.recv(4096)
                        if not data:
                            break
                        f.write(data)
                        received += len(data)
                        self.progress_updated.emit(int(received * 100 / file_size))

                if received == file_size:
                    actual_size = os.path.getsize(save_path)
                    if actual_size == file_size:
                        self.download_finished.emit(str(save_path))
                    else:
                        os.remove(save_path)
                        self.error_occurred.emit(f"文件不完整")
                else:
                    os.remove(save_path)
                    self.error_occurred.emit(f"下载中断")
        except Exception as e:
            self.error_occurred.emit(f"{file_info['filename']} 下载失败: {str(e)}")

    def cancel(self):
        self._cancel = True


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.server = FileServer()
        self.current_downloads = []
        self.scanner = None
        self.searcher = None
        self.download_tasks = []
        self.temp_dirs = []
        self.import_worker = None
        self.import_thread = None

        self.init_ui()
        self.init_server()
        self.init_menu()
        self.is_searching = False
        self.setup_shortcuts()


    def init_menu(self):
        """初始化菜单栏"""
        menu_bar = self.menuBar()
        file_menu = menu_bar.addMenu("数据管理")

        # 导入功能
        import_action = QAction(QIcon(), "导入货品数据", self)
        import_action.triggered.connect(self.handle_import)
        file_menu.addAction(import_action)
        # 新增视频水印功能
        watermark_action = QAction(QIcon(), "添加视频水印", self)
        watermark_action.triggered.connect(self.handle_add_watermark)
        file_menu.addAction(watermark_action)

    def handle_import(self):
        """处理Excel导入"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel文件 (*.xlsx *.xls)"
        )

        if not file_path:
            return

        # 初始化进度对话框
        self.progress_dialog = QProgressDialog("正在导入数据...", "取消", 0, 100, self)
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.show()

        # 创建导入线程
        self.import_thread = QThread()
        self.import_worker = excel_importer.ExcelImporter(CONFIG["DB"])
        self.import_worker.moveToThread(self.import_thread)

        # 连接信号
        self.import_thread.started.connect(
            lambda: self.import_worker.run_import(file_path)
        )
        self.import_worker.progress.connect(self.progress_dialog.setValue)
        self.import_worker.finished.connect(self.handle_import_result)
        self.import_worker.finished.connect(self.import_thread.quit)
        self.import_worker.finished.connect(self.import_worker.deleteLater)
        self.import_thread.finished.connect(self.import_thread.deleteLater)

        # 启动线程
        self.import_thread.start()

    def handle_import_result(self, success, message):
        """处理导入结果"""
        self.progress_dialog.close()
        if success:
            QMessageBox.information(self, "导入完成", message)
        else:
            QMessageBox.critical(self, "导入错误", message)

    def handle_add_watermark(self):
        """处理视频水印添加"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv)"
        )

        if not file_path:
            return

        # 创建处理线程
        self.watermark_thread = QThread()
        self.watermark_worker = WatermarkWorker(file_path)
        self.watermark_worker.moveToThread(self.watermark_thread)

        # 进度对话框
        self.watermark_dialog = QProgressDialog("正在添加水印...", "取消", 0, 100, self)
        self.watermark_dialog.setWindowModality(Qt.WindowModality.WindowModal)

        # 信号连接
        self.watermark_thread.started.connect(self.watermark_worker.process)
        self.watermark_worker.progress.connect(self.watermark_dialog.setValue)
        self.watermark_worker.finished.connect(self.handle_watermark_result)
        self.watermark_worker.finished.connect(self.watermark_thread.quit)
        self.watermark_dialog.canceled.connect(self.watermark_worker.cancel)

        self.watermark_thread.start()
        self.watermark_dialog.show()

    def handle_watermark_result(self, success: bool, message: str):
        """处理水印添加结果"""
        self.watermark_dialog.close()
        if success:
            QMessageBox.information(self, "处理成功", message)
        else:
            QMessageBox.critical(self, "处理失败", message)
    def init_ui(self):
        self.setWindowTitle("智能视频管理系统")
        self.setWindowIcon(QIcon("icon.ico"))
        self.resize(1200, 800)

        self.setStyleSheet("""
            QMainWindow { background: #f0f2f5; }
            QLineEdit { 
                padding: 8px; border: 1px solid #dcdfe6; 
                border-radius: 4px; font-size: 14px;
            }
            QPushButton {
                background: #409eff; color: white; padding: 8px 16px;
                border-radius: 4px; border: none;
            }
            QPushButton:hover { background: #66b1ff; }
            QListWidget {
                background: white; border: 1px solid #ebeef5;
                border-radius: 4px; font-size: 13px;
            }
            QLabel { color: #606266; }
        """)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入单号（例如：YT232434）")
        self.search_btn = QPushButton("搜索")

        self.device_list = QListWidget()
        self.device_list.setFixedHeight(100)

        self.result_list = EnhancedListWidget()
        self.result_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_list.customContextMenuRequested.connect(self.show_context_menu)

        self.status_bar = self.statusBar()

        main_layout = QVBoxLayout()
        header = QHBoxLayout()
        header.addWidget(self.search_input)
        header.addWidget(self.search_btn)

        main_layout.addLayout(header)
        main_layout.addWidget(QLabel("在线设备:"))
        main_layout.addWidget(self.device_list)
        main_layout.addWidget(QLabel("搜索结果:"))
        main_layout.addWidget(self.result_list)

        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

        self.search_btn.clicked.connect(self.start_search)
        self.server.status_changed.connect(self.status_bar.showMessage)
        self.result_list.itemDoubleClicked.connect(self.handle_item_double_click)

    def init_server(self):
        port = CONFIG["PORT"]
        if self.is_port_in_use(port):
            reply = QMessageBox.question(
                self, "端口占用",
                f"端口 {port} 已被占用，是否尝试终止占用进程？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                if self.kill_process_on_port(port):
                    QMessageBox.information(self, "成功", "进程已终止，正在启动服务...")
                else:
                    QMessageBox.critical(self, "错误", "无法终止占用进程，请手动关闭相关程序后重试。")
                    self.close()
                    return
            else:
                self.close()
                return
        self.server.start()
        QTimer.singleShot(1000, self.check_server_status)

    def is_port_in_use(self, port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('127.0.0.1', port)) == 0

    def kill_process_on_port(self, port):
        try:
            result = subprocess.check_output(
                f'netstat -ano | findstr :{port} | findstr LISTENING',
                shell=True,
                stderr=subprocess.STDOUT
            )
            lines = result.decode('gbk').strip().split('\r\n')
            for line in lines:
                parts = line.split()
                pid = parts[-1]
                subprocess.run(f'taskkill /PID {pid} /F', shell=True, check=True)
                return True
        except subprocess.CalledProcessError as e:
            logging.error(f"终止进程失败: {e.output.decode('gbk')}")
            return False
        except Exception as e:
            logging.error(f"Error killing process: {str(e)}")
            return False

    def check_server_status(self):
        if not self.server.isRunning():
            QMessageBox.critical(self, "错误", "服务启动失败，请检查端口占用")
            self.close()

    def setup_shortcuts(self):
        self.search_input.returnPressed.connect(self.start_search)
        QShortcut(QKeySequence("Esc"), self).activated.connect(self.close)

    def start_search(self):
        if self.is_searching:
            QMessageBox.warning(self, "提示", "当前已有搜索正在进行，请稍候")
            return
        sn = self.search_input.text().strip().upper()
        if not sn:
            self.show_error("请输入有效的单号")
            return
        self.is_searching = True
        self.result_list.clear()
        self.status_bar.showMessage("正在扫描网络...")

        self.scanner = NetworkScanner()
        self.scanner.progress_updated.connect(
            lambda v: self.status_bar.showMessage(f"网络扫描进度：{v}%"))
        self.scanner.scan_finished.connect(
            lambda ips: self.start_file_search(sn, ips))
        self.scanner.finished.connect(lambda: setattr(self, 'is_searching', False))
        self.scanner.start()

    def start_file_search(self, sn, active_ips):
        self.device_list.clear()

        # 获取本机IP并过滤设备列表
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()

            # 过滤掉本机IP，只显示远程设备
            remote_ips = [ip for ip in active_ips if ip != local_ip]
            if remote_ips:
                self.device_list.addItems(remote_ips)
            else:
                self.device_list.addItem("无远程设备")

            logging.info(f"设备列表: 本机IP={local_ip}, 远程设备={len(remote_ips)}个")
        except Exception as e:
            logging.error(f"过滤设备列表失败: {str(e)}")
            self.device_list.addItems(active_ips)

        self.searcher = FileSearcher(sn, active_ips)
        self.progress = QProgressDialog("正在搜索文件...", "取消", 0, 100, self)
        self.progress.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress.canceled.connect(self.cancel_search)

        self.searcher.search_finished.connect(self.show_results)
        self.searcher.error_occurred.connect(self.show_error)
        self.searcher.finished.connect(self.search_finished)
        self.searcher.start()

    def cancel_search(self):
        if self.scanner and self.scanner.isRunning():
            self.scanner.terminate()
        if self.searcher and self.searcher.isRunning():
            self.searcher.terminate()
        self.is_searching = False
        self.status_bar.showMessage("搜索已取消")

    def closeEvent(self, event):
        for temp_dir in self.temp_dirs:
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                logging.info(f"清理临时目录: {temp_dir}")
            except Exception as e:
                logging.error(f"清理临时目录失败：{str(e)}")

        self.server.stop()

        threads = [self.scanner, self.searcher] + self.download_tasks
        for t in threads:
            try:
                if t and t.isRunning():
                    t.terminate()
                    t.wait(2000)
                    if t.isRunning():
                        t.quit()
            except Exception as e:
                logging.error(f"终止线程失败: {str(e)}")

        event.accept()

    def search_finished(self):
        self.is_searching = False
        self.status_bar.showMessage("搜索完成")

    def show_results(self, results):
        self.progress.close()
        if not results:
            self.show_info("未找到相关文件")
            return

        unique_files = {}
        for item in results:
            if not all(k in item for k in ['filename', 'path', 'local']):
                continue
            key = item['path']
            if key in unique_files:
                # 如果当前项目是本地文件，则替换已存在的项目（本地优先）
                if item['local']:
                    unique_files[key] = item
                # 如果已存在的是本地文件，当前项目是远程文件，则保持本地文件不变
                elif unique_files[key]['local']:
                    continue
                # 如果都是远程文件，保持第一个找到的
                else:
                    continue
            else:
                unique_files[key] = item

        for item in unique_files.values():
            list_item = SearchResultItem(item)
            self.result_list.addItem(list_item)

        self.status_bar.showMessage(f"找到 {len(unique_files)} 个文件")

    def show_context_menu(self, pos):
        menu = QMenu()
        selected = self.result_list.selectedItems()

        if not selected:
            return

        # 检查选中的文件类型
        has_local = any(item.file_info['local'] for item in selected)
        has_remote = any(not item.file_info['local'] for item in selected)

        play_action = QAction("播放视频", self)
        play_action.triggered.connect(lambda: self.play_videos(selected))
        menu.addAction(play_action)

        locate_action = QAction("定位文件位置", self)
        locate_action.triggered.connect(lambda: self.locate_files(selected))
        menu.addAction(locate_action)

        # 只有远程文件时才显示下载选项
        if has_remote:
            download_action = QAction("下载远程文件", self)
            download_action.triggered.connect(lambda: self.download_files(selected))
            menu.addAction(download_action)

        # 只有本地文件时显示复制选项
        if has_local and not has_remote:
            copy_action = QAction("复制到指定位置", self)
            copy_action.triggered.connect(lambda: self.copy_local_files(selected))
            menu.addAction(copy_action)

        menu.exec(self.result_list.mapToGlobal(pos))

    def play_videos(self, items):
        for item in items:
            file_info = item.file_info
            if file_info['local']:
                self.play_local_video(file_info)
            else:
                self.handle_remote_play(file_info)

    def play_local_video(self, file_info):
        path = Path(file_info['path'])
        if not path.exists():
            QMessageBox.warning(self, "错误", f"本地文件不存在：{path}")
            return

        try:
            logging.info(f"尝试播放本地视频：{path}")
            success = QDesktopServices.openUrl(QUrl.fromLocalFile(str(path)))
            if not success:
                raise RuntimeError("系统未找到可用播放器")
            logging.info(f"本地视频播放成功：{path}")
        except Exception as e:
            logging.error(f"播放本地视频失败：{path}, 错误：{str(e)}")
            QMessageBox.critical(self, "播放错误", f"无法播放视频：{str(e)}")

    def handle_remote_play(self, file_info):
        reply = QMessageBox.question(
            self, "远程播放",
            "需要先下载远程文件才能播放，是否立即下载？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.download_and_play(file_info)

    def download_and_play(self, file_info):
        temp_dir = Path(tempfile.mkdtemp(prefix="video_player_"))
        self.temp_dirs.append(temp_dir)
        downloader = DownloadManager([file_info], temp_dir, self)
        dialog = DownloadDialog(1, self)

        downloader.download_finished.connect(
            lambda path: self.on_download_complete(path))
        downloader.error_occurred.connect(
            lambda msg: self.show_error(f"下载失败：{msg}"))

        downloader.progress_updated.connect(dialog.update_progress)
        dialog.cancel_requested.connect(downloader.cancel)

        dialog.show()
        downloader.start()

    def on_download_complete(self, path):
        try:
            if Path(path).stat().st_size == 0:
                raise ValueError("文件大小为0")

            for retry in range(3):
                if QDesktopServices.openUrl(QUrl.fromLocalFile(str(path))):
                    return
            raise RuntimeError("播放器启动失败")
        except Exception as e:
            self.show_error(f"播放失败：{str(e)}")

    def download_files(self, items):
        save_dir = QFileDialog.getExistingDirectory(
            self, "选择保存目录", str(Path.home() / "Desktop"))
        if not save_dir:
            return

        files = [item.file_info for item in items if not item.file_info['local']]
        if not files:
            QMessageBox.information(self, "提示", "没有需要下载的远程文件")
            return

        downloader = DownloadManager(files, save_dir, self)
        dialog = DownloadDialog(len(files), self)
        downloader.progress_updated.connect(dialog.update_progress)
        downloader.download_finished.connect(dialog.add_success)
        downloader.error_occurred.connect(dialog.add_error)
        downloader.finished.connect(dialog.finalize)

        dialog.cancel_requested.connect(downloader.cancel)
        dialog.show()
        downloader.start()

    def copy_local_files(self, items):
        save_dir = QFileDialog.getExistingDirectory(
            self, "选择复制目标目录", str(Path.home() / "Desktop"))
        if not save_dir:
            return

        local_files = [item.file_info for item in items if item.file_info['local']]
        if not local_files:
            QMessageBox.information(self, "提示", "没有需要复制的本地文件")
            return

        success_count = 0
        error_count = 0

        for file_info in local_files:
            try:
                src_path = Path(file_info['path'])
                if not src_path.exists():
                    error_count += 1
                    continue

                dst_path = Path(save_dir) / file_info['filename']
                import shutil
                shutil.copy2(src_path, dst_path)
                success_count += 1
            except Exception as e:
                logging.error(f"复制文件失败：{file_info['path']}, 错误：{str(e)}")
                error_count += 1

        QMessageBox.information(self, "复制完成",
            f"复制完成！\n成功: {success_count}\n失败: {error_count}")

    def handle_item_double_click(self, item):
        if item.file_info['local']:
            self.play_local_video(item.file_info)
        else:
            self.download_files([item])

    def open_local_file(self, file_info):
        path = Path(file_info['path'])
        if not path.exists():
            self.show_error("文件不存在")
            return
        QDesktopServices.openUrl(QUrl.fromLocalFile(str(path.parent)))

    def locate_files(self, items):
        for item in items:
            file_info = item.file_info
            if file_info['local']:
                path = Path(file_info['path'])
                if path.exists():
                    QDesktopServices.openUrl(QUrl.fromLocalFile(str(path.parent)))
                else:
                    QMessageBox.warning(self, "错误", f"本地文件不存在：{path}")
            else:
                QMessageBox.information(self, "提示",
                    f"远程文件位于：{file_info['ip']}\n"
                    f"文件路径：{file_info['path']}\n"
                    f"请使用下载功能获取文件到本地")

    def show_error(self, message):
        QMessageBox.critical(self, "错误", message)

    def show_info(self, message):
        QMessageBox.information(self, "提示", message)
class WatermarkWorker(QObject):
    progress = pyqtSignal(int)
    finished = pyqtSignal(bool, str)

    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self._cancel = False

    def process(self):
        """执行处理任务"""
        try:
            output_path = video_processor.VideoProcessor.add_watermark(self.file_path)
            self.finished.emit(True, f"水印添加成功！\n输出路径：{output_path}")
        except Exception as e:
            self.finished.emit(False, f"处理失败：{str(e)}")

    def cancel(self):
        """取消处理"""
        self._cancel = True
class EnhancedListWidget(QListWidget):
    def __init__(self):
        super().__init__()
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

class SearchResultItem(QListWidgetItem):
    def __init__(self, file_info):
        super().__init__()
        self.file_info = file_info
        self.setText(self.format_text())
        self.set_background()

    def format_text(self):
        local_flag = "📌" if self.file_info['local'] else "🌐"
        return (
            f"{local_flag} [{self.file_info['drive']}盘] "
            f"{self.file_info['filename']} | "
            f"{self.file_info['ip']} | "
            f"{self.format_size(self.file_info['size'])} | "
            f"{self.format_date(self.file_info['mtime'])}"
        )

    def format_size(self, size):
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} GB"

    def format_date(self, timestamp):
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')

    def set_background(self):
        color = QColor('#e8f4ff') if self.file_info['local'] else QColor('white')
        self.setBackground(color)

class DownloadDialog(QDialog):
    cancel_requested = pyqtSignal()

    def __init__(self, total_files, parent=None):
        super().__init__(parent)
        self.setWindowTitle("下载进度")
        self.setFixedSize(500, 200)

        layout = QVBoxLayout()
        self.total_bar = QProgressBar()
        self.current_bar = QProgressBar()
        self.stats_label = QLabel("准备下载...")
        self.log_area = QListWidget()

        button_box = QDialogButtonBox()
        self.cancel_btn = button_box.addButton("取消", QDialogButtonBox.ButtonRole.RejectRole)

        layout.addWidget(QLabel("总体进度:"))
        layout.addWidget(self.total_bar)
        layout.addWidget(QLabel("当前文件进度:"))
        layout.addWidget(self.current_bar)
        layout.addWidget(self.stats_label)
        layout.addWidget(self.log_area)
        layout.addWidget(button_box)

        self.setLayout(layout)

        self.success_count = 0
        self.error_count = 0
        self.total_files = total_files

        self.cancel_btn.clicked.connect(self.cancel)

    def update_progress(self, value):
        self.current_bar.setValue(value)
        self.total_bar.setValue(int((self.success_count + self.error_count) * 100 / self.total_files))

    def add_success(self, path):
        self.success_count += 1
        self.log_area.addItem(f"✓ 成功下载: {Path(path).name}")
        self.update_stats()

    def add_error(self, message):
        self.error_count += 1
        self.log_area.addItem(f"✕ 错误: {message}")
        self.update_stats()

    def update_stats(self):
        self.stats_label.setText(
            f"已完成: {self.success_count + self.error_count}/{self.total_files} "
            f"(成功: {self.success_count}, 失败: {self.error_count})"
        )

    def finalize(self):
        self.cancel_btn.setText("关闭")
        self.cancel_btn.disconnect()
        self.cancel_btn.clicked.connect(self.accept)
        QMessageBox.information(self, "下载完成",
                                f"下载完成！\n成功: {self.success_count}\n失败: {self.error_count}")

    def cancel(self):
        self.cancel_requested.emit()
        self.reject()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    window = MainWindow()
    window.show()
    sys.exit(app.exec())