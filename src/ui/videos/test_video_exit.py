#!/usr/bin/env python3
# test_video_exit.py - 测试视频录制退出机制的完整性

import time
import threading
import logging
import os
import cv2
import numpy as np
from optimized_video_recorder import OptimizedVideoRecorder
from config import EnhancedConfig

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("VideoExitTest")

class VideoExitTester:
    """视频退出机制测试器"""
    
    def __init__(self):
        self.config = EnhancedConfig()
        self.test_results = []
        
    def create_test_frame(self, frame_number):
        """创建测试帧"""
        frame = np.zeros((self.config.resolution[1], self.config.resolution[0], 3), dtype=np.uint8)
        # 在帧上绘制帧号
        cv2.putText(frame, f"Frame {frame_number}", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        return frame
    
    def test_normal_exit(self):
        """测试正常退出流程"""
        logger.info("=== 测试正常退出流程 ===")
        
        recorder = OptimizedVideoRecorder(
            self.config.resolution,
            self.config.recording_fps,
            self.config.save_path,
            self.config.max_duration
        )
        
        # 激活录制
        test_identifier = f"TEST_NORMAL_{int(time.time())}"
        recorder.switch_identifier(test_identifier)
        
        # 等待录制器准备就绪
        time.sleep(1.0)
        
        # 添加测试帧
        frame_count = 50
        logger.info(f"添加 {frame_count} 个测试帧...")
        for i in range(frame_count):
            frame = self.create_test_frame(i)
            recorder.add_frame(frame)
            time.sleep(0.01)  # 模拟实际帧率
        
        # 记录停止前状态
        stats_before = recorder.get_stats()
        logger.info(f"停止前状态: {stats_before}")
        
        # 正常停止
        recorder.stop()
        
        # 验证结果
        video_file = os.path.join(self.config.save_path, 
                                 time.strftime("%Y%m%d"), 
                                 f"{test_identifier}.mp4")
        
        result = {
            'test_type': 'normal_exit',
            'frames_added': frame_count,
            'stats_before_stop': stats_before,
            'video_file_exists': os.path.exists(video_file),
            'video_file_size': os.path.getsize(video_file) if os.path.exists(video_file) else 0
        }
        
        self.test_results.append(result)
        logger.info(f"正常退出测试结果: {result}")
        
        return result
    
    def test_emergency_exit(self):
        """测试紧急退出流程（模拟ESC按键）"""
        logger.info("=== 测试紧急退出流程 ===")
        
        recorder = OptimizedVideoRecorder(
            self.config.resolution,
            self.config.recording_fps,
            self.config.save_path,
            self.config.max_duration
        )
        
        # 激活录制
        test_identifier = f"TEST_EMERGENCY_{int(time.time())}"
        recorder.switch_identifier(test_identifier)
        
        # 等待录制器准备就绪
        time.sleep(1.0)
        
        # 快速添加大量帧，模拟高负载情况
        frame_count = 100
        logger.info(f"快速添加 {frame_count} 个测试帧...")
        for i in range(frame_count):
            frame = self.create_test_frame(i)
            recorder.add_frame(frame)
            # 不等待，模拟高频帧输入
        
        # 立即停止（模拟ESC退出）
        stats_before = recorder.get_stats()
        logger.info(f"紧急停止前状态: {stats_before}")
        
        recorder.stop()
        
        # 验证结果
        video_file = os.path.join(self.config.save_path, 
                                 time.strftime("%Y%m%d"), 
                                 f"{test_identifier}.mp4")
        
        result = {
            'test_type': 'emergency_exit',
            'frames_added': frame_count,
            'stats_before_stop': stats_before,
            'video_file_exists': os.path.exists(video_file),
            'video_file_size': os.path.getsize(video_file) if os.path.exists(video_file) else 0
        }
        
        self.test_results.append(result)
        logger.info(f"紧急退出测试结果: {result}")
        
        return result
    
    def analyze_video_integrity(self, video_file):
        """分析视频文件完整性"""
        if not os.path.exists(video_file):
            return {'error': 'Video file not found'}
        
        try:
            cap = cv2.VideoCapture(video_file)
            if not cap.isOpened():
                return {'error': 'Cannot open video file'}
            
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            return {
                'frame_count': frame_count,
                'fps': fps,
                'duration': duration,
                'file_size': os.path.getsize(video_file)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始视频退出机制测试...")
        
        # 确保保存目录存在
        os.makedirs(self.config.save_path, exist_ok=True)
        
        # 运行测试
        normal_result = self.test_normal_exit()
        time.sleep(2)  # 等待文件系统同步
        
        emergency_result = self.test_emergency_exit()
        time.sleep(2)  # 等待文件系统同步
        
        # 分析结果
        logger.info("=== 测试结果分析 ===")
        for result in self.test_results:
            logger.info(f"测试类型: {result['test_type']}")
            logger.info(f"添加帧数: {result['frames_added']}")
            logger.info(f"停止前队列: {result['stats_before_stop']['queue_size']}")
            logger.info(f"停止前缓冲: {result['stats_before_stop']['buffer_size']}")
            logger.info(f"视频文件存在: {result['video_file_exists']}")
            logger.info(f"视频文件大小: {result['video_file_size']} bytes")
            logger.info("-" * 40)
        
        return self.test_results

if __name__ == "__main__":
    tester = VideoExitTester()
    results = tester.run_all_tests()
    
    # 输出最终报告
    print("\n" + "="*60)
    print("视频退出机制测试报告")
    print("="*60)
    
    for result in results:
        print(f"\n测试类型: {result['test_type']}")
        print(f"结果: {'通过' if result['video_file_exists'] and result['video_file_size'] > 0 else '失败'}")
        if result['video_file_exists']:
            print(f"视频文件大小: {result['video_file_size']:,} bytes")
    
    print("\n测试完成！")
