# optimized_video_recorder.py - 优化的视频录制器，解决录制延迟问题
import threading
import time
import cv2
import os
import logging
from datetime import datetime
from queue import Queue, Empty
import concurrent.futures

logger = logging.getLogger("LogisticsScanner")

class OptimizedVideoRecorder(threading.Thread):
    """优化的视频录制器，异步文件创建，无阻塞切换"""

    def __init__(self, resolution, fps, save_path, max_duration, tts_manager=None):
        super().__init__(daemon=True)
        self.resolution = resolution
        self.fps = fps
        self.save_path = save_path
        self.max_duration = max_duration
        self.tts_manager = tts_manager
        
        # 录制状态
        self.running = True
        self.current_identifier = None
        self.current_writer = None
        self.current_filename = ""
        self.video_start_time = 0
        self.frame_counter = 0
        
        # 帧队列 - 增大容量以应对高频切换
        self.frame_queue = Queue(maxsize=500)
        
        # 异步文件创建
        self.file_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=1, 
            thread_name_prefix="VideoFileCreator"
        )
        self.pending_writer_future = None
        self.pending_identifier = None
        
        # 性能优化
        self.current_date_dir = self._get_date_dir()
        self.frames_buffer = []
        self.buffer_size = 30  # 批量写入大小
        self.last_perf_log = 0
        
        # 启动录制线程
        self.start()

    def _get_date_dir(self):
        """获取当前日期目录"""
        date_str = datetime.now().strftime("%Y%m%d")
        date_dir = os.path.join(self.save_path, date_str)
        os.makedirs(date_dir, exist_ok=True)
        return date_dir

    def _create_writer_async(self, identifier):
        """异步创建视频写入器"""
        try:
            # 确保日期目录存在
            current_date_dir = self._get_date_dir()
            filename = os.path.join(current_date_dir, f"{identifier}.mp4")
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'H264')
            
            # 尝试硬件加速
            try:
                writer = cv2.VideoWriter(
                    filename, fourcc, self.fps, self.resolution,
                    params=[cv2.VIDEOWRITER_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY]
                )
            except:
                # 回退到标准编码
                writer = cv2.VideoWriter(filename, fourcc, self.fps, self.resolution)
            
            if writer.isOpened():
                logger.info(f"异步创建视频文件成功: {filename}")
                return writer, filename, identifier
            else:
                logger.error(f"异步创建视频写入器失败: {filename}")
                return None, "", identifier
                
        except Exception as e:
            logger.error(f"异步创建视频写入器异常: {e}")
            return None, "", identifier

    def switch_identifier(self, identifier):
        """快速切换录制标识符（非阻塞）"""
        try:
            # 检查是否与当前标识符相同
            if self.current_identifier == identifier:
                logger.debug(f"相同标识符已激活: {identifier}，跳过切换")
                return
            
            # 立即更新标识符状态（非阻塞）
            old_identifier = self.current_identifier
            self.current_identifier = identifier
            
            if identifier:
                # 异步创建新的视频写入器
                self.pending_identifier = identifier
                self.pending_writer_future = self.file_executor.submit(
                    self._create_writer_async, identifier
                )
                
                # 立即触发语音播报（异步）
                if self.tts_manager:
                    try:
                        if hasattr(self.tts_manager, 'speak_recording_start'):
                            self.tts_manager.speak_recording_start()
                        logger.debug(f"已触发语音播报: {identifier}")
                    except Exception as e:
                        logger.warning(f"语音播报失败: {e}")
                
                logger.info(f"快速切换到: {identifier} (异步创建中...)")
            else:
                # 停止录制
                if self.current_writer:
                    self.current_writer.release()
                    self.current_writer = None
                    logger.info(f"停止录制: {old_identifier}")
                
                # 取消待处理的创建任务
                if self.pending_writer_future:
                    self.pending_writer_future.cancel()
                    self.pending_writer_future = None
                
                self.current_filename = ""
                logger.info("录制已停止")
            
        except Exception as e:
            logger.error(f"切换标识符失败: {e}")

    def _check_pending_writer(self):
        """检查并应用待处理的视频写入器"""
        if self.pending_writer_future and self.pending_writer_future.done():
            try:
                writer, filename, identifier = self.pending_writer_future.result()
                
                # 检查标识符是否仍然匹配
                if identifier == self.current_identifier:
                    # 关闭旧的写入器
                    if self.current_writer:
                        self.current_writer.release()
                    
                    # 应用新的写入器
                    self.current_writer = writer
                    self.current_filename = filename
                    self.video_start_time = time.time()
                    self.frame_counter = 0
                    
                    if writer:
                        logger.info(f"视频写入器就绪: {identifier}")
                    else:
                        logger.error(f"视频写入器创建失败: {identifier}")
                        self.current_identifier = None
                else:
                    # 标识符已变化，丢弃这个写入器
                    if writer:
                        writer.release()
                    logger.debug(f"丢弃过期的写入器: {identifier}")
                
            except Exception as e:
                logger.error(f"应用视频写入器失败: {e}")
            finally:
                self.pending_writer_future = None

    def add_frame(self, frame):
        """添加帧到录制队列（非阻塞）"""
        if not self.running:
            return False
        
        try:
            # 非阻塞添加帧
            self.frame_queue.put(frame, block=False)
            return True
        except:
            # 队列满时丢弃帧
            return False

    def run(self):
        """录制线程主循环"""
        logger.info("优化视频录制线程启动")
        
        while self.running:
            try:
                # 检查待处理的视频写入器
                self._check_pending_writer()
                
                # 处理帧队列
                try:
                    frame = self.frame_queue.get(timeout=0.1)
                    if frame is None:  # 停止信号
                        break
                    
                    # 批量处理帧
                    self.frames_buffer.append(frame)
                    
                    # 当缓冲区满或有写入器时批量写入
                    if (len(self.frames_buffer) >= self.buffer_size or 
                        (self.current_writer and len(self.frames_buffer) > 0)):
                        self._write_buffered_frames()
                    
                except Empty:
                    # 超时时也尝试写入缓冲的帧
                    if self.frames_buffer and self.current_writer:
                        self._write_buffered_frames()
                    continue
                
                # 性能日志
                current_time = time.time()
                if current_time - self.last_perf_log > 10:
                    queue_size = self.frame_queue.qsize()
                    buffer_size = len(self.frames_buffer)
                    logger.debug(f"录制性能: 队列={queue_size}, 缓冲={buffer_size}, 帧数={self.frame_counter}")
                    self.last_perf_log = current_time
                
            except Exception as e:
                logger.error(f"录制线程异常: {e}")
                time.sleep(0.1)
        
        # 清理资源
        self._cleanup()
        logger.info("优化视频录制线程结束")

    def _write_buffered_frames(self):
        """批量写入缓冲的帧"""
        if not self.current_writer or not self.frames_buffer:
            return
        
        try:
            for frame in self.frames_buffer:
                self.current_writer.write(frame)
                self.frame_counter += 1
            
            self.frames_buffer.clear()
            
        except Exception as e:
            logger.error(f"批量写入帧失败: {e}")
            self.frames_buffer.clear()

    def _cleanup(self):
        """清理资源 - 增强版，确保完整性"""
        try:
            logger.info("开始清理视频录制资源...")

            # 记录清理前的状态
            buffer_count = len(self.frames_buffer) if self.frames_buffer else 0
            queue_count = self.frame_queue.qsize()
            logger.info(f"清理前状态: 缓冲帧={buffer_count}, 队列帧={queue_count}, 总录制帧={self.frame_counter}")

            # 写入剩余帧
            if self.frames_buffer and self.current_writer:
                logger.info(f"写入剩余的 {len(self.frames_buffer)} 帧...")
                self._write_buffered_frames()
                logger.info("剩余帧写入完成")

            # 关闭当前写入器
            if self.current_writer:
                logger.info(f"关闭视频文件: {self.current_filename}")
                self.current_writer.release()
                self.current_writer = None
                logger.info(f"视频文件已保存，总帧数: {self.frame_counter}")

            # 取消待处理任务
            if self.pending_writer_future:
                logger.info("取消待处理的文件创建任务...")
                self.pending_writer_future.cancel()

            # 关闭线程池
            logger.info("关闭文件创建线程池...")
            self.file_executor.shutdown(wait=True)  # 改为等待完成
            logger.info("线程池已关闭")

            logger.info("视频录制资源清理完成")

        except Exception as e:
            logger.error(f"清理资源失败: {e}", exc_info=True)

    def stop(self):
        """停止录制 - 增强版，确保所有帧都被写入"""
        logger.info("停止优化视频录制器...")
        self.running = False

        # 🔧 修复：确保所有待处理的帧都被写入
        try:
            # 1. 等待异步文件创建完成
            if self.pending_writer_future and not self.pending_writer_future.done():
                logger.info("等待异步文件创建完成...")
                try:
                    self.pending_writer_future.result(timeout=3.0)
                    logger.info("异步文件创建已完成")
                except Exception as e:
                    logger.warning(f"异步文件创建超时或失败: {e}")

            # 2. 处理队列中剩余的帧
            remaining_frames = []
            logger.info(f"处理队列中剩余的 {self.frame_queue.qsize()} 帧...")
            while not self.frame_queue.empty():
                try:
                    frame = self.frame_queue.get_nowait()
                    if frame is not None:
                        remaining_frames.append(frame)
                except:
                    break

            # 3. 写入剩余帧到缓冲区
            if remaining_frames:
                self.frames_buffer.extend(remaining_frames)
                logger.info(f"添加 {len(remaining_frames)} 帧到缓冲区")

            # 4. 强制写入所有缓冲的帧
            if self.frames_buffer and self.current_writer:
                logger.info(f"强制写入 {len(self.frames_buffer)} 帧...")
                self._write_buffered_frames()
                logger.info("所有缓冲帧已写入完成")

            # 5. 发送停止信号
            self.frame_queue.put(None, timeout=1.0)

        except Exception as e:
            logger.error(f"停止录制时处理剩余帧失败: {e}")

        # 等待线程结束，增加超时时间
        if self.is_alive():
            logger.info("等待录制线程安全退出...")
            self.join(timeout=10.0)  # 增加到10秒
            if self.is_alive():
                logger.warning("录制线程未能及时停止，可能存在帧丢失风险")
            else:
                logger.info("录制线程已安全退出")

    def get_stats(self):
        """获取录制统计信息"""
        return {
            'current_identifier': self.current_identifier,
            'frame_counter': self.frame_counter,
            'queue_size': self.frame_queue.qsize(),
            'buffer_size': len(self.frames_buffer),
            'is_recording': self.current_writer is not None,
            'pending_creation': self.pending_writer_future is not None
        }
