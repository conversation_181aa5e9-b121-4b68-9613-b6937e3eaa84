# logistics_scanner.py
import threading
import time
import cv2
import re
import os
import logging
import keyboard
import psutil
import numpy as np
import queue
from collections import OrderedDict, defaultdict
from queue import Empty
import mysql.connector
import gc
import weakref
from config import EnhancedConfig
from database import DatabaseManager
from optimized_video_recorder import OptimizedVideoRecorder
from ui_renderer import LogisticsScannerUI
from performance_monitor import PerformanceMonitor, MemoryOptimizer
from resource_manager import start_resource_monitoring, get_resource_manager
from async_tts import get_async_tts

logger = logging.getLogger("LogisticsScanner")


class LogisticsScanner:
    def __init__(self):
        self.config = EnhancedConfig()
        self._init_resources()
        self._init_state()
        self._init_scan_params()
        self.scan_thread = threading.Thread(target=self._scan_listener, daemon=True)
        self.scan_thread.start()

        # 启动扫描处理线程
        self.scan_processor_thread = threading.Thread(target=self._scan_processor, daemon=True)
        self.scan_processor_thread.start()
        self.lock = threading.RLock()
        self.ui_renderer = LogisticsScannerUI(self.config.font_path, self.config.font_size)
        self.render_buffer = None
        self.last_capture_time = 0
        self.capture_interval = 1.0 / self.config.camera_fps  # 基于配置的帧率设置
        self.ui_dirty = True
        self.last_frame = None
        self._init_ui_buffer()

        # 🔧 修复：初始化增强的资源管理
        self.performance_monitor = PerformanceMonitor()
        self.memory_optimizer = MemoryOptimizer()
        self.resource_manager = start_resource_monitoring()

        self.performance_monitor.start()
        self.memory_optimizer.start()

        # 注册关键对象到资源管理器
        self.resource_manager.register_object(self.video_recorder)
        self.resource_manager.register_object(self.ui_renderer)

    def _init_ui_buffer(self):
        """初始化UI缓冲区 - 优化版"""
        self.ui_cache = weakref.WeakValueDictionary()  # 使用弱引用缓存，自动清理
        self.ui_cache_key = None  # 当前缓存键
        self.ui_force_refresh_interval = 0.1  # 减少强制刷新间隔，提高响应性
        self.last_ui_force_refresh = 0
        self.ui_dirty = True
        self.max_cache_size = 10  # 限制缓存大小
        self.cache_hit_count = 0
        self.cache_miss_count = 0

    def _init_resources(self):
        """初始化系统资源"""
        try:
            self.cap = cv2.VideoCapture(0)
            if not self.cap.isOpened():
                raise RuntimeError("无法打开摄像头")
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.resolution[0])
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.resolution[1])
            self.cap.set(cv2.CAP_PROP_FPS, self.config.camera_fps)

            # 获取数据库连接池
            db_pool = self.config.get_db_pool()
            self.db = DatabaseManager(db_pool)
            logger.info("数据库连接池初始化成功")

            # 初始化AsyncTTS
            self.tts_manager = get_async_tts(self.config)
            if self.tts_manager.is_available():
                logger.info("AsyncTTS语音播报功能已启用")
            else:
                logger.warning("AsyncTTS语音播报功能不可用")

            self.video_recorder = OptimizedVideoRecorder(
                self.config.resolution,
                self.config.recording_fps,
                self.config.save_path,
                self.config.max_duration,
                self.tts_manager
            )

            try:
                p = psutil.Process(os.getpid())
                p.nice(psutil.HIGH_PRIORITY_CLASS)
                logger.info("设置进程优先级为高")
            except Exception as e:
                logger.warning(f"优先级设置失败: {str(e)}")

        except Exception as e:
            logger.critical(f"初始化失败: {str(e)}")
            self.cleanup()
            raise

    def _init_state(self):
        """初始化状态变量"""
        self.running = True
        self.active_identifier = None
        self.identifier_type = None
        self.scanned_data = defaultdict(OrderedDict)
        self.current_scans = OrderedDict()  # 存储扫描记录 {barcode: {product_code, quantity}}
        self.total_quantity = 0
        self.scroll_offset = 0
        self.scan_buffer = []
        self.scan_enabled = True
        self.last_scan_time = 0
        self.last_ui_force_refresh = time.time()
        self.ui_force_refresh_interval = 0.2
        self.order_items = {}  # 存储当前订单的商品信息 {product_code: max_quantity}
        self.order_products = set()  # 当前订单包含的商品编码

    def _init_scan_params(self):
        """初始化扫描参数"""
        self.SCAN_DEBOUNCE = 0.05  # 减少到50毫秒，提高响应速度
        self.BARCODE_MIN_LENGTH = 5
        self.max_history_items = 1000

        # 添加扫描事件队列和处理机制
        self.scan_queue = queue.Queue(maxsize=100)  # 扫描事件队列
        self.scan_processor_thread = None
        self.scan_stats = {
            'total_scans': 0,
            'processed_scans': 0,
            'dropped_scans': 0,
            'last_scan_time': 0
        }

    def _record_frame(self, frame):
        """统一处理视频帧录制 - 超高性能版"""
        try:
            if self.video_recorder and self.video_recorder.frame_queue.qsize() < 250:
                # 只在队列未满时添加帧，避免内存积压
                try:
                    # 使用视图而不是复制，减少内存使用
                    frame_view = np.ascontiguousarray(frame)
                    self.video_recorder.frame_queue.put_nowait(frame_view)
                except:
                    # 队列满时静默丢弃，避免阻塞主线程
                    pass
        except Exception as e:
            logger.error(f"视频帧录制失败: {e}")

    def _activate_identifier(self, value):
        """激活新单号/物流号"""
        with self.lock:
            # 检查是否是新物流单号
            if self.active_identifier == value:
                logger.info(f"相同物流单号已激活: {value}，跳过初始化")
                return
            self.identifier_type = 'shipping'
            self.active_identifier = value.upper()
            self.ui_dirty = True  # 标记UI需要更新

            # 初始化扫描数据
            self.current_scans = self.scanned_data.setdefault(self.active_identifier, OrderedDict())
            self.total_quantity = 0

            # 重置界面滚动
            self.scroll_offset = 0

            # 通知录制线程切换单号
            self.video_recorder.switch_identifier(self.active_identifier)

            logger.info(f"激活物流单号: {self.active_identifier}")

    def _load_order_items(self, identifier):
        """从数据库加载订单商品信息"""
        try:
            logger.info(f"查询物流单信息: {identifier}")
            # 修正查询语句以匹配实际数据库字段
            query = f"""
                SELECT itemCode as product_code, quantity 
                FROM orders 
                WHERE primaryWaybillCode  = %s
            """
            logger.debug(f"执行查询: {query} with {identifier}")
            result = self.db.execute_query(query, (identifier,))

            if result is None:
                logger.error("数据库查询返回None")
                return False

            if not result:
                logger.warning(f"未检索到相关物流单号: {identifier}")
                return False

            # 加载订单商品信息
            self.order_items = {}
            self.order_products = set()
            for item in result:
                product_code = item['product_code'].strip().upper()
                quantity = item['quantity']
                self.order_items[product_code] = quantity
                self.order_products.add(product_code)

            logger.info(f"加载物流单商品信息: 找到 {len(self.order_items)} 种商品: {list(self.order_products)}")
            return True

        except Exception as e:
            logger.error(f"加载物流单信息时出错: {e}", exc_info=True)
            return False

    def _process_shipping_code(self, code):
        """处理物流单号"""
        code = code.upper()
        logger.debug(f"处理物流单号: {code}")
        # 检查是否与当前激活的单号相同
        if self.active_identifier == code:
            logger.info(f"重复扫描相同的物流单号: {code}，忽略")
            return

        if re.match(r'^(YT\d{11,13}|SF\d{12,14}|9\d{11,13}|PK\d{16,18})$', code):
            # 查询数据库
            if self._load_order_items(code):
                self._activate_identifier(code)
            else:
                logger.warning(f"物流单号未找到: {code}")
        else:
            logger.warning(f"无效物流单号格式: {code}")

    def _process_barcode(self, barcode):
        """处理商品条码"""
        if not self.active_identifier:
            logger.warning("请先扫描单号")
            return

        # 使用独立线程处理数据库查询，避免阻塞主线程
        threading.Thread(
            target=self._process_barcode_db, args=(barcode,), daemon=True
        ).start()

    def _process_barcode_db(self, barcode):
        """数据库查询处理（在独立线程中执行）- 优化版"""
        product_code = None
        try:
            logger.debug(f"查询条码: {barcode}")
            # 使用预编译查询提高性能
            result = self.db.execute_query_cached(
                "SELECT product_code FROM products WHERE barcode = %s",
                (barcode.strip(),)
            )

            if result is None:
                logger.error("商品查询返回None")
                return

            if not result:
                logger.warning(f"无效条码: {barcode}")
                return

            # 获取所有匹配的商品编码（去重）- 优化内存使用
            product_codes = {item['product_code'].strip().upper() for item in result}

            logger.debug(f"查询条码结果: 条码={barcode}, 匹配商品编码={len(product_codes)}个")

            # 减少锁的持有时间
            order_products_copy = None
            with self.lock:
                order_products_copy = self.order_products.copy()

            # 在锁外进行集合运算
            valid_codes = product_codes & order_products_copy

            if not valid_codes:
                logger.warning(f"未找到订单中的匹配商品: {barcode}")
                return

            if len(valid_codes) > 1:
                logger.error(f"条码 {barcode} 匹配多个订单商品: {list(valid_codes)}")
                return

            # 取唯一匹配的商品编码
            product_code = next(iter(valid_codes))
        except mysql.connector.Error as err:
            logger.error(f"数据库错误: {err}")
            return
        except Exception as e:
            logger.error(f"条码处理异常: {e}")
            return

        # 只在更新共享状态时加锁，减少锁竞争
        if product_code:
            with self.lock:
                # 获取当前扫描的商品项
                item = self.current_scans.get(barcode, {
                    "code": product_code,
                    "quantity": 0,
                    "max_quantity": self.order_items.get(product_code, 0)
                })

                # 检查是否超过最大数量
                if item['quantity'] >= item['max_quantity']:
                    logger.warning(f"商品 {product_code} 数量已达上限 {item['max_quantity']}")
                    return

                # 更新数量
                item['quantity'] += 1
                self.current_scans[barcode] = item
                self.total_quantity += 1
                self.ui_dirty = True  # 标记UI需要更新
                logger.info(
                    f"扫描商品: {product_code}，当前: {item['quantity']}/{item['max_quantity']}，总计: {self.total_quantity}")

                # 异步清理历史数据，避免阻塞
                if len(self.scanned_data) > self.max_history_items:
                    threading.Thread(target=self._purge_old_data, daemon=True).start()

    def get_scan_statistics(self):
        """获取扫描统计信息"""
        if not hasattr(self, 'scan_stats'):
            return "扫描统计未初始化"

        stats = self.scan_stats
        success_rate = (stats['processed_scans'] / stats['total_scans'] * 100) if stats['total_scans'] > 0 else 0
        queue_size = self.scan_queue.qsize() if hasattr(self, 'scan_queue') else 0

        return (f"扫描统计 - 总计: {stats['total_scans']}, "
                f"处理: {stats['processed_scans']}, "
                f"丢弃: {stats['dropped_scans']}, "
                f"成功率: {success_rate:.1f}%, "
                f"队列: {queue_size}")

    def _get_summarized_scans(self):
        """按商品编码汇总扫描数据"""
        summary = defaultdict(lambda: {
            'code': '',
            'order_quantity': 0,
            'actual_quantity': 0
        })

        # 汇总当前扫描数据
        for item in self.current_scans.values():
            product_code = item['code']
            if product_code in summary:
                summary[product_code]['actual_quantity'] += item['quantity']
            else:
                summary[product_code] = {
                    'code': product_code,
                    'order_quantity': item['max_quantity'],
                    'actual_quantity': item['quantity']
                }

        # 添加订单中有但尚未扫描的商品
        for product_code, max_quantity in self.order_items.items():
            if product_code not in summary:
                summary[product_code] = {
                    'code': product_code,
                    'order_quantity': max_quantity,
                    'actual_quantity': 0
                }

        # 转换为列表并按商品编码排序
        return sorted(summary.values(), key=lambda x: x['code'])

    def _purge_old_data(self):
        """定期清理旧的扫描数据，防止内存溢出"""
        with self.lock:
            if len(self.scanned_data) > self.max_history_items:
                # 删除最早的记录
                oldest_identifier = next(iter(self.scanned_data))
                self.scanned_data.pop(oldest_identifier, None)
                logger.info(f"清理历史数据: {oldest_identifier}")

    def _scan_listener(self):
        """扫描输入监听线程 - 优化版"""
        logger.info("扫描监听线程启动")
        keyboard.hook(self._on_key_event)
        while self.running:
            time.sleep(0.01)  # 减少休眠时间到10毫秒，提高响应性
        keyboard.unhook_all()
        logger.info("扫描监听线程停止")

    def _scan_processor(self):
        """扫描事件处理线程 - 专门处理扫描队列"""
        logger.info("扫描处理线程启动")
        while self.running:
            try:
                # 从队列获取扫描事件，超时1秒
                scan_data = self.scan_queue.get(timeout=1.0)
                if scan_data is None:  # 停止信号
                    break

                # 处理扫描数据
                self._process_input_internal(scan_data['code'], scan_data['timestamp'])
                self.scan_stats['processed_scans'] += 1

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"扫描处理异常: {e}")

        logger.info("扫描处理线程停止")

    def _on_key_event(self, event):
        """键盘事件处理 - 优化版"""
        if not self.scan_enabled or event.event_type != keyboard.KEY_DOWN:
            return

        if event.name == 'enter':
            if self.scan_buffer:
                input_str = ''.join(self.scan_buffer).upper()
                self.scan_buffer.clear()
                timestamp = time.time()

                # 记录扫描统计
                self.scan_stats['total_scans'] += 1
                self.scan_stats['last_scan_time'] = timestamp

                logger.debug(f"接收扫描输入: {input_str} (时间戳: {timestamp})")

                # 将扫描事件放入队列，避免阻塞键盘监听
                try:
                    self.scan_queue.put_nowait({
                        'code': input_str,
                        'timestamp': timestamp
                    })
                except queue.Full:
                    self.scan_stats['dropped_scans'] += 1
                    logger.warning(f"扫描队列已满，丢弃扫描: {input_str}")

        elif event.name == 'backspace' and self.scan_buffer:
            self.scan_buffer.pop()
        elif len(event.name) == 1 and (event.name.isalnum() or event.name in '*+-'):
            self.scan_buffer.append(event.name)

    def _process_input(self, code):
        """输入内容处理 - 保持向后兼容"""
        timestamp = time.time()
        self._process_input_internal(code, timestamp)

    def _process_input_internal(self, code, timestamp):
        """内部输入处理逻辑 - 优化版"""
        # 更精确的防抖动检查，使用传入的时间戳
        if timestamp - self.last_scan_time < self.SCAN_DEBOUNCE:
            logger.debug(f"防抖动过滤: {code} (间隔: {timestamp - self.last_scan_time:.3f}s)")
            return

        self.last_scan_time = timestamp
        logger.debug(f"处理输入: {code} (时间戳: {timestamp})")

        # 优先处理单号
        if re.match(r'^(YT\d{11,13}|SF\d{12,14}|9\d{11,13}|PK\d{16,18})$', code):
            self._process_shipping_code(code)
        elif len(code) >= self.BARCODE_MIN_LENGTH:
            self._process_barcode(code)
        else:
            logger.warning(f"未知输入: {code}")

    def run(self):
        """主运行循环 - 超级优化版本"""
        try:
            frame_counter = 0
            last_fps_time = time.time()
            last_perf_log = time.time()
            last_vr_log = time.time()
            last_gc_time = time.time()  # 垃圾回收时间

            # 创建前台和后台缓冲区
            front_buffer = None
            back_buffer = None

            # 预分配变量减少内存分配
            summarized_scans = []
            cache_key = None

            while self.running:
                current_time = time.time()
                frame_delay = current_time - self.last_capture_time

                # 精确控制帧率 - 优化休眠策略
                if frame_delay < self.capture_interval:
                    sleep_time = self.capture_interval - frame_delay
                    if sleep_time > 0.002:  # 只有足够长的时间才休眠
                        time.sleep(sleep_time - 0.001)
                    continue

                self.last_capture_time = current_time
                frame_counter += 1

                # 捕获帧
                ret, frame = self.cap.read()
                if not ret:
                    logger.error("摄像头读取失败")
                    break

                # 每次都更新基础帧，确保画面实时更新
                self.last_frame = frame

                # 简化UI渲染逻辑，减少CPU使用
                with self.lock:
                    # 按商品编码汇总扫描数据
                    summarized_scans = self._get_summarized_scans()

                    # 检查是否需要强制刷新UI
                    if current_time - self.last_ui_force_refresh > self.ui_force_refresh_interval:
                        self.ui_dirty = True
                        self.last_ui_force_refresh = current_time

                # 直接渲染UI，不使用复杂缓存
                display_frame = self.ui_renderer.draw_interface(
                    frame,
                    self.active_identifier,
                    summarized_scans,
                    self.total_quantity,
                    self.scroll_offset
                )

                with self.lock:
                    self.ui_dirty = False

                # 录制帧（包含完整UI界面）
                self._record_frame(display_frame)

                # 显示界面
                cv2.imshow('Logistics Scanner', display_frame)

                # FPS计算和性能监控
                if current_time - last_fps_time >= 1.0:
                    fps = frame_counter / (current_time - last_fps_time)
                    frame_counter = 0
                    last_fps_time = current_time

                    # 更新性能监控器
                    self.performance_monitor.update_fps(fps)

                    # 性能日志 - 大幅减少频率，优化显示格式
                    if current_time - last_perf_log > 30:  # 改为30秒输出一次
                        last_perf_log = current_time
                        vr_queue = self.video_recorder.frame_queue.qsize() if self.video_recorder else 0
                        perf_stats = self.performance_monitor.get_performance_stats()
                        scan_stats = self.get_scan_statistics()
                        logger.debug(
                            f"主循环FPS: {fps:.1f} | 录制队列: {vr_queue} | "
                            f"CPU: {perf_stats['cpu']:.1f}% (标准化: {perf_stats.get('cpu_normalized', 0):.1f}%) | "
                            f"内存: {perf_stats['memory']:.1f}% | CPU核心: {perf_stats.get('cpu_cores', 0)}个 | "
                            f"{scan_stats}"
                        )

                # 🔧 修复：优化垃圾回收策略，防止阻塞
                if current_time - last_gc_time > 120:  # 每2分钟进行一次垃圾回收
                    last_gc_time = current_time
                    # 🔧 修复：使用异步垃圾回收，避免阻塞主线程
                    def async_gc():
                        try:
                            collected = gc.collect()
                            if collected > 0:
                                logger.debug(f"垃圾回收: 清理了 {collected} 个对象")
                        except Exception as e:
                            logger.warning(f"垃圾回收失败: {e}")

                    threading.Thread(target=async_gc, daemon=True).start()

                # 处理键盘事件
                key = cv2.waitKey(1) & 0xFF
                if key == 27:  # ESC退出
                    logger.info("检测到ESC退出信号，开始安全退出流程...")
                    self.running = False

                    # 🔧 修复：在退出前确保视频录制完整性
                    if hasattr(self, 'video_recorder') and self.video_recorder:
                        queue_size = self.video_recorder.frame_queue.qsize()
                        buffer_size = len(self.video_recorder.frames_buffer) if self.video_recorder.frames_buffer else 0
                        if queue_size > 0 or buffer_size > 0:
                            logger.info(f"退出前状态检查: 队列帧={queue_size}, 缓冲帧={buffer_size}")
                            logger.info("等待视频帧写入完成...")
                            # 给录制器一些时间处理剩余帧
                            time.sleep(0.5)

                    self.cleanup()
                    return
                elif key == ord('s'):  # 切换扫描状态
                    self.scan_enabled = not self.scan_enabled
                    status = "启用" if self.scan_enabled else "禁用"
                    logger.info(f"扫描状态: {status}")
                elif key == ord('c'):  # 清空当前扫描数据
                    with self.lock:
                        self.current_scans.clear()
                        self.total_quantity = 0
                        self.ui_dirty = True
                        logger.info("已清空当前扫描数据")
                elif key == ord('u'):  # 上滚
                    with self.lock:
                        if self.scroll_offset > 0:
                            self.scroll_offset = max(0, self.scroll_offset - 1)
                            self.ui_dirty = True
                elif key == ord('d'):  # 下滚
                    with self.lock:
                        if len(summarized_scans) > 15:
                            self.scroll_offset = min(len(summarized_scans) - 15, self.scroll_offset + 1)
                            self.ui_dirty = True

                # 定期记录录制线程状态
                if current_time - last_vr_log > 15:  # 减少日志频率
                    last_vr_log = current_time
                    if self.video_recorder:
                        vr_queue = self.video_recorder.frame_queue.qsize()
                        if vr_queue > 100:  # 只在队列较长时记录
                            logger.info(f"录制队列状态: {vr_queue}帧待处理")

        except Exception as e:
            logger.critical(f"主循环异常: {str(e)}", exc_info=True)
        finally:
            self.cleanup()

    def cleanup(self):
        """资源清理 - 优化退出处理"""
        self.running = False
        logger.info("开始系统清理...")

        # 🔧 修复：停止所有监控服务
        if hasattr(self, 'performance_monitor'):
            self.performance_monitor.stop()
        if hasattr(self, 'memory_optimizer'):
            self.memory_optimizer.stop()
        if hasattr(self, 'resource_manager'):
            self.resource_manager.stop()

        # 停止扫描处理线程
        if hasattr(self, 'scan_processor_thread') and self.scan_processor_thread.is_alive():
            # 发送停止信号到队列
            try:
                self.scan_queue.put_nowait(None)
            except queue.Full:
                pass
            self.scan_processor_thread.join(timeout=1.0)
            if self.scan_processor_thread.is_alive():
                logger.warning("扫描处理线程未能及时停止")
            else:
                logger.info("扫描处理线程已停止")

        # 停止扫描监听线程
        if hasattr(self, 'scan_thread') and self.scan_thread.is_alive():
            # 等待扫描线程安全退出
            self.scan_thread.join(timeout=1.0)
            if self.scan_thread.is_alive():
                logger.warning("扫描监听线程未能及时停止")
            else:
                logger.info("扫描监听线程已停止")

        # 输出扫描统计信息
        if hasattr(self, 'scan_stats'):
            logger.info(f"扫描统计 - 总计: {self.scan_stats['total_scans']}, "
                       f"处理: {self.scan_stats['processed_scans']}, "
                       f"丢弃: {self.scan_stats['dropped_scans']}")

        # 安全停止视频录制线程
        if hasattr(self, 'video_recorder'):
            logger.info("停止视频录制线程...")

            # 🔧 修复：记录停止前的录制状态
            if self.video_recorder:
                stats = self.video_recorder.get_stats()
                logger.info(f"停止前录制状态: {stats}")

                # 检查是否有未完成的录制
                if stats['queue_size'] > 0 or stats['buffer_size'] > 0:
                    logger.warning(f"检测到未处理的帧: 队列={stats['queue_size']}, 缓冲={stats['buffer_size']}")
                    logger.info("正在确保所有帧都被写入...")

            self.video_recorder.stop()
            logger.info("视频录制线程已停止")

        # 停止AsyncTTS
        if hasattr(self, 'tts_manager'):
            logger.info("停止AsyncTTS语音播报...")
            self.tts_manager.stop()
            logger.info("AsyncTTS语音播报已停止")

        # 释放摄像头资源
        if hasattr(self, 'cap') and self.cap.isOpened():
            logger.info("释放摄像头资源...")
            self.cap.release()

        # 清理UI缓存
        if hasattr(self, 'ui_cache'):
            self.ui_cache.clear()

        # 强制垃圾回收
        gc.collect()

        # 关闭所有OpenCV窗口
        cv2.destroyAllWindows()
        logger.info("系统已安全关闭")