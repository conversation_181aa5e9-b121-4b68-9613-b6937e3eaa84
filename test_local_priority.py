#!/usr/bin/env python3
"""
测试本地文件优先显示逻辑
"""

def test_local_priority_logic():
    """测试本地文件优先逻辑"""
    
    # 模拟搜索结果数据
    test_results = [
        # 远程文件先出现
        {'filename': 'video1.mp4', 'path': 'D:/videos/20250903/video1.mp4', 'local': False, 'ip': '*************'},
        # 本地文件后出现（应该替换远程文件）
        {'filename': 'video1.mp4', 'path': 'D:/videos/20250903/video1.mp4', 'local': True, 'ip': '本机'},
        # 另一个远程文件
        {'filename': 'video2.mp4', 'path': 'D:/videos/20250903/video2.mp4', 'local': False, 'ip': '*************'},
        # 只有本地的文件
        {'filename': 'video3.mp4', 'path': 'D:/videos/20250903/video3.mp4', 'local': True, 'ip': '本机'},
        # 本地文件先出现
        {'filename': 'video4.mp4', 'path': 'D:/videos/20250903/video4.mp4', 'local': True, 'ip': '本机'},
        # 远程文件后出现（应该被忽略）
        {'filename': 'video4.mp4', 'path': 'D:/videos/20250903/video4.mp4', 'local': False, 'ip': '*************'},
    ]
    
    # 应用新的去重逻辑
    unique_files = {}
    for item in test_results:
        if not all(k in item for k in ['filename', 'path', 'local']):
            continue
        key = item['path']
        if key in unique_files:
            # 如果当前项目是本地文件，则替换已存在的项目（本地优先）
            if item['local']:
                unique_files[key] = item
            # 如果已存在的是本地文件，当前项目是远程文件，则保持本地文件不变
            elif unique_files[key]['local']:
                continue
            # 如果都是远程文件，保持第一个找到的
            else:
                continue
        else:
            unique_files[key] = item
    
    # 验证结果
    print("去重后的结果:")
    for path, item in unique_files.items():
        local_flag = "📌本地" if item['local'] else "🌐远程"
        print(f"{local_flag} - {item['filename']} - {item['ip']}")
    
    # 验证期望结果
    expected_local_count = 3  # video1, video3, video4 应该都是本地
    expected_remote_count = 1  # video2 应该是远程
    
    actual_local_count = sum(1 for item in unique_files.values() if item['local'])
    actual_remote_count = sum(1 for item in unique_files.values() if not item['local'])
    
    print(f"\n验证结果:")
    print(f"期望本地文件数: {expected_local_count}, 实际: {actual_local_count}")
    print(f"期望远程文件数: {expected_remote_count}, 实际: {actual_remote_count}")
    
    # 具体验证每个文件
    assert unique_files['D:/videos/20250903/video1.mp4']['local'] == True, "video1 应该是本地文件"
    assert unique_files['D:/videos/20250903/video2.mp4']['local'] == False, "video2 应该是远程文件"
    assert unique_files['D:/videos/20250903/video3.mp4']['local'] == True, "video3 应该是本地文件"
    assert unique_files['D:/videos/20250903/video4.mp4']['local'] == True, "video4 应该是本地文件"
    
    print("✅ 所有测试通过！本地文件优先逻辑工作正常。")

if __name__ == "__main__":
    test_local_priority_logic()
